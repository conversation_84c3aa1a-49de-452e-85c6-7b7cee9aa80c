from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.edge.service import Service
from selenium.webdriver.edge.options import Options
from webdriver_manager.microsoft import EdgeChromiumDriverManager
from dotenv import dotenv_values
import os
import mtranslate as mt
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# Load environment variables from the .env file.
env_vars = dotenv_values(".env")
# Get the input language setting from the environment variables, default to "en" if not set.
InputLanguage = env_vars.get("InputLanguage", "en")

def setup_edge_webdriver():
    """
    Setup Edge WebDriver with multiple fallback options.
    Focuses on Edge since that's the preferred browser.
    """
    print("Setting up Edge WebDriver...")

    # Configure Edge options
    edge_options = Options()
    user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/*********** Safari"
    edge_options.add_argument(f'user-agent={user_agent}')
    edge_options.add_argument("--use-fake-ui-for-media-stream")
    edge_options.add_argument("--use-fake-device-for-media-stream")
    edge_options.add_argument("--headless")  # Headless mode (runs in the background)
    edge_options.add_argument("--disable-gpu")  # To ensure smoother operation in headless mode
    edge_options.add_argument("--window-size=1920x1080")  # Set window size
    edge_options.add_argument("--disable-web-security")
    edge_options.add_argument("--allow-running-insecure-content")
    edge_options.add_argument("--disable-extensions")
    edge_options.add_argument("--no-sandbox")  # Additional stability option
    edge_options.add_argument("--disable-dev-shm-usage")  # Additional stability option

    # Method 1: Try local paths first (most reliable)
    print("Trying local Edge WebDriver paths...")
    edge_paths = [
        r"msedgedriver.exe",  # If it's in PATH or current directory
        r".\msedgedriver.exe",  # Current directory
        r"C:\Program Files (x86)\Microsoft\Edge\Application\msedgedriver.exe",
        r"C:\Program Files\Microsoft\Edge\Application\msedgedriver.exe",
        r"C:\Windows\System32\msedgedriver.exe",
        r"C:\Windows\msedgedriver.exe"
    ]

    for path in edge_paths:
        try:
            if path == "msedgedriver.exe" or path == r".\msedgedriver.exe" or os.path.exists(path):
                print(f"Trying Edge WebDriver at: {path}")
                service = Service(path)
                driver = webdriver.Edge(service=service, options=edge_options)
                print(f"✓ Edge WebDriver setup successful with local driver: {path}")
                return driver
        except Exception as path_error:
            print(f"Failed with path {path}: {path_error}")
            continue

    # Method 2: Try webdriver-manager (requires internet)
    print("Trying webdriver-manager (requires internet connection)...")
    try:
        service = Service(EdgeChromiumDriverManager().install())
        driver = webdriver.Edge(service=service, options=edge_options)
        print("✓ Edge WebDriver setup successful with webdriver-manager")
        return driver
    except Exception as e:
        print(f"webdriver-manager failed (likely due to network issues): {e}")

    # Method 3: Try without specifying service (use system Edge)
    print("Trying to use system Edge WebDriver...")
    try:
        driver = webdriver.Edge(options=edge_options)
        print("✓ Edge WebDriver setup successful using system driver")
        return driver
    except Exception as e:
        print(f"System Edge WebDriver failed: {e}")

    # Method 4: Try with minimal options (last resort)
    print("Trying Edge with minimal options...")
    try:
        minimal_options = Options()
        minimal_options.add_argument("--use-fake-ui-for-media-stream")
        minimal_options.add_argument("--use-fake-device-for-media-stream")
        # Remove headless mode as last resort
        driver = webdriver.Edge(options=minimal_options)
        print("✓ Edge WebDriver setup successful with minimal options (non-headless)")
        return driver
    except Exception as e:
        print(f"Minimal Edge WebDriver failed: {e}")

    # If all methods fail, provide detailed instructions
    raise Exception(f"""
    ❌ Edge WebDriver setup failed!

    SOLUTION: Download Edge WebDriver manually

    1. Find your Edge browser version:
       - Open Microsoft Edge
       - Go to Settings > About Microsoft Edge
       - Note the version number (e.g., 120.0.2210.144)

    2. Download matching Edge WebDriver:
       - Go to: https://developer.microsoft.com/en-us/microsoft-edge/tools/webdriver/
       - Download the version that matches your Edge browser
       - Extract the 'msedgedriver.exe' file

    3. Place the driver file:
       - Put 'msedgedriver.exe' in your project directory: {os.getcwd()}
       - OR add it to your Windows PATH environment variable

    4. Run the script again

    Current working directory: {os.getcwd()}
    """)

# Debugging print to check if InputLanguage is loaded correctly.
print(f"Input Language: {InputLanguage}")

# Define the HTML code for the speech recognition interface.
HtmlCode = '''<!DOCTYPE html>
<html lang="en">
<head>
    <title>Speech Recognition</title>
</head>
<body>
    <button id="start" onclick="startRecognition()">Start Recognition</button>
    <button id="end" onclick="stopRecognition()">Stop Recognition</button>
    <p id="output"></p>
    <script>
        const output = document.getElementById('output');
        let recognition;

        function startRecognition() {
            recognition = new webkitSpeechRecognition() || new SpeechRecognition();
            recognition.lang = '';
            recognition.continuous = true;

            recognition.onresult = function(event) {
                const transcript = event.results[event.results.length - 1][0].transcript;
                output.textContent += transcript;
            };

            recognition.onend = function() {
                recognition.start();
            };
            recognition.start();
        }

        function stopRecognition() {
            recognition.stop();
            output.innerHTML = "";
        }
    </script>
</body>
</html>'''

# Replace the language setting in the HTML code with the input language from the environment variables.
HtmlCode = str(HtmlCode).replace("recognition.lang = '';", f"recognition.lang = '{InputLanguage}';")

# Write the modified HTML code to a file.
with open(r"DataVoice.html", "w") as f:
    f.write(HtmlCode)

# Get the current working directory.
current_dir = os.getcwd()

# Generate the absolute file path for the HTML file.
Link = f"file:///{os.path.abspath('DataVoice.html')}"
print(f"Attempting to open file at: {Link}")  # Debugging print statement

# Initialize Edge WebDriver with fallback options
driver = setup_edge_webdriver()

# Define the path for temporary files.
TempDirPath = rf"{current_dir}/Frontend/Files"

# Function to set the assistant's status by writing it to a file.
def SetAssistantStatus(Status):
    with open(rf"{TempDirPath}/Status.data", "w", encoding="utf-8") as file:
        file.write(Status)

# Function to modify a query to ensure proper punctuation and formatting.
def QueryModifier(Query):
    new_query = Query.lower().strip()
    query_words = new_query.split()
    question_words = ["how", "what", "who", "where", "when", "why", "which", "whose", "whom", "can you", "what's", "wh"]

    # Check if the query is a question and add a question mark if necessary.
    if any(word + " " in new_query for word in question_words):
        if query_words[-1][-1] in ['.', '?', '!']:
            new_query = new_query[:-1] + '?'
        else:
            new_query += '?'
    else:
        # Add a period if the query is not a question.
        if query_words[-1][-1] in ['.', '?', '!']:
            new_query = new_query[:-1] + '.'
        else:
            new_query += '.'
    return new_query

# Function to translate text into English using the mtranslate library.
def UniversalTranslator(Text):
    english_translation = mt.translate(Text, "en", "auto")
    return english_translation.capitalize()

# Function to perform speech recognition using the WebDriver.
def SpeechRecognition():
    # Open the HTML file in the browser.
    driver.get(Link)
    
    # Wait until the 'start' button is available, then click it to start speech recognition.
    WebDriverWait(driver, 10).until(EC.element_to_be_clickable((By.ID, "start"))).click()

    while True:
        try:
            # Get the recognized text from the HTML output element.
            Text = driver.find_element(by=By.ID, value="output").text

            if Text:
                # Stop recognition by clicking the stop button.
                driver.find_element(by=By.ID, value="end").click()
                return Text
        except:
            continue

# Main execution block.
if __name__ == "__main__":
    while True:
        try:
            # Continually perform speech recognition and print the recognized text.
            Text = SpeechRecognition()
            if str(InputLanguage).lower() == "en" or "en" in str(InputLanguage).lower():
                print(QueryModifier(Text))
            else:
                SetAssistantStatus('Translating ...')
                print(QueryModifier(UniversalTranslator(Text)))
        except Exception as e:
            print(f"An error occurred: {e}")
            break