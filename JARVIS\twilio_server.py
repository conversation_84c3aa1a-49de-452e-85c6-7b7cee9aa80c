#!/usr/bin/env python3
"""
Twilio Webhook Server for JARVIS AI Assistant
Handles incoming voice calls and SMS messages through Twilio webhooks
Integrates with existing JARVIS functionality for AI responses
"""

from flask import Flask, request, Response
from twilio.twiml.voice_response import VoiceResponse
from twilio.twiml.messaging_response import MessagingResponse
from twilio.rest import Client
from dotenv import dotenv_values
import logging
import os
import sys
import tempfile
import requests
from urllib.parse import urljoin
import threading
import time

# Add the JARVIS directory to Python path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import JARVIS modules
try:
    from Backend.Chatbot import ChatBot
    from Backend.RealtimeSearchEngine import RealtimeSearchEngine
    from Backend.Model import FirstLayerDMM
    from Backend.TextToSpeech import TextToSpeech
    from Frontend.GUI import QueryModifier
    from Backend.TwilioIntegration import (
        voice_processor, sms_processor, call_manager,
        create_welcome_voice_response, process_voice_input, process_sms_message
    )
except ImportError as e:
    print(f"Warning: Could not import JARVIS modules: {e}")
    print("Running in standalone mode...")

# Load environment variables
env_vars = dotenv_values(".env")

# Twilio configuration
TWILIO_ACCOUNT_SID = env_vars.get("TWILIO_ACCOUNT_SID")
TWILIO_AUTH_TOKEN = env_vars.get("TWILIO_AUTH_TOKEN")
TWILIO_PHONE_NUMBER = env_vars.get("TWILIO_PHONE_NUMBER")

# JARVIS configuration
Username = env_vars.get("Username", "User")
Assistantname = env_vars.get("Assistantname", "Jarvis")

# Initialize Flask app
app = Flask(__name__)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Twilio client
twilio_client = None
if TWILIO_ACCOUNT_SID and TWILIO_AUTH_TOKEN:
    twilio_client = Client(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)
    logger.info("Twilio client initialized successfully")
else:
    logger.warning("Twilio credentials not found in .env file")

class JarvisProcessor:
    """Handles processing of user queries through JARVIS AI"""
    
    def __init__(self):
        self.functions = ["open", "close", "play", "system", "content", "google search", "youtube search"]
    
    def process_query(self, query):
        """Process a query through JARVIS and return response"""
        try:
            logger.info(f"Processing query: {query}")
            
            # Use JARVIS decision making model
            decision = FirstLayerDMM(query)
            logger.info(f"Decision: {decision}")
            
            # Check for general and realtime queries
            general_queries = [i for i in decision if i.startswith("general")]
            realtime_queries = [i for i in decision if i.startswith("realtime")]
            
            if realtime_queries or (general_queries and realtime_queries):
                # Handle realtime search
                merged_query = " and ".join([" ".join(i.split()[1:]) for i in decision 
                                           if i.startswith("general") or i.startswith("realtime")])
                response = RealtimeSearchEngine(QueryModifier(merged_query))
            elif general_queries:
                # Handle general chat
                query_final = general_queries[0].replace("general ", "")
                response = ChatBot(QueryModifier(query_final))
            else:
                # Default to chatbot
                response = ChatBot(QueryModifier(query))
            
            logger.info(f"Generated response: {response[:100]}...")
            return response
            
        except Exception as e:
            logger.error(f"Error processing query: {e}")
            return f"I'm sorry, I encountered an error processing your request. Please try again."

# Initialize JARVIS processor
jarvis = JarvisProcessor()

@app.route("/webhook/voice", methods=['POST'])
def handle_voice_call():
    """Handle incoming voice calls from Twilio"""
    logger.info("Received voice call webhook")

    # Get call information
    call_sid = request.form.get('CallSid')
    caller_number = request.form.get('From')

    # Start call session
    call_manager.start_call_session(call_sid, caller_number)

    # Create welcome response using the integration module
    response = create_welcome_voice_response()

    return Response(str(response), mimetype='text/xml')

@app.route("/webhook/voice/process", methods=['POST'])
def process_voice_recording():
    """Process the recorded voice message"""
    logger.info("Processing voice recording")

    # Get input data
    speech_result = request.form.get('SpeechResult')
    digits = request.form.get('Digits')
    call_sid = request.form.get('CallSid')

    # Process using integration module
    response = process_voice_input(speech_result, digits, call_sid)

    return Response(str(response), mimetype='text/xml')

@app.route("/webhook/voice/options", methods=['POST'])
def handle_voice_options():
    """Handle voice options menu"""
    logger.info("Handling voice options")

    response = voice_processor.create_options_menu()
    return Response(str(response), mimetype='text/xml')

@app.route("/webhook/voice/handle_option", methods=['POST'])
def handle_voice_option_selection():
    """Handle voice option selection"""
    logger.info("Handling voice option selection")

    digits = request.form.get('Digits')
    speech_result = request.form.get('SpeechResult')
    call_sid = request.form.get('CallSid')

    response = process_voice_input(speech_result, digits, call_sid)
    return Response(str(response), mimetype='text/xml')

@app.route("/webhook/voice/transcription", methods=['POST'])
def handle_transcription():
    """Handle transcription callback from Twilio"""
    logger.info("Received transcription callback")
    
    transcription_text = request.form.get('TranscriptionText', '')
    caller_number = request.form.get('From')
    
    if transcription_text:
        logger.info(f"Transcription: {transcription_text}")
        
        # Process through JARVIS
        jarvis_response = jarvis.process_query(transcription_text)
        
        # You could store this response and use it in a follow-up call
        # For now, we'll just log it
        logger.info(f"JARVIS response: {jarvis_response}")
        
        # Optionally, send response via SMS
        if twilio_client and caller_number:
            try:
                message = twilio_client.messages.create(
                    body=f"{Assistantname}: {jarvis_response}",
                    from_=TWILIO_PHONE_NUMBER,
                    to=caller_number
                )
                logger.info(f"Sent SMS response: {message.sid}")
            except Exception as e:
                logger.error(f"Failed to send SMS: {e}")
    
    return Response("OK", mimetype='text/plain')

@app.route("/webhook/sms", methods=['POST'])
def handle_sms():
    """Handle incoming SMS messages"""
    logger.info("Received SMS webhook")

    # Get message details
    message_body = request.form.get('Body', '').strip()
    sender_number = request.form.get('From')

    logger.info(f"SMS from {sender_number}: {message_body}")

    # Process using integration module
    response = process_sms_message(message_body, sender_number)

    return Response(str(response), mimetype='text/xml')

@app.route("/health", methods=['GET'])
def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "JARVIS Twilio Webhook Server",
        "assistant": Assistantname
    }

@app.route("/", methods=['GET'])
def index():
    """Root endpoint with basic info"""
    return f"""
    <h1>{Assistantname} Twilio Webhook Server</h1>
    <p>Server is running and ready to handle Twilio webhooks.</p>
    <h2>Available Endpoints:</h2>
    <ul>
        <li><strong>/webhook/voice</strong> - Handle incoming voice calls</li>
        <li><strong>/webhook/sms</strong> - Handle incoming SMS messages</li>
        <li><strong>/health</strong> - Health check</li>
    </ul>
    <p>Configure your Twilio phone number to use these webhook URLs.</p>
    """

if __name__ == "__main__":
    # Check if Twilio credentials are configured
    if not all([TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN, TWILIO_PHONE_NUMBER]):
        logger.warning("Twilio credentials not fully configured. Please check your .env file.")
        logger.info("Required variables: TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN, TWILIO_PHONE_NUMBER")
    
    # Start the Flask server
    logger.info(f"Starting {Assistantname} Twilio Webhook Server...")
    logger.info("Server will be available at http://localhost:5000")
    
    app.run(
        host='0.0.0.0',  # Allow external connections
        port=5000,
        debug=False,  # Set to False for production
        threaded=True
    )
