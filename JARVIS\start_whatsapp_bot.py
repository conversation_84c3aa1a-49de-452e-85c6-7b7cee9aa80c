#!/usr/bin/env python3
"""
Startup script for JARVIS WhatsApp Bot
Automatically starts ngrok tunnel and Flask WhatsApp server
"""

import subprocess
import time
import sys
import os
import json
import requests
from dotenv import dotenv_values
import threading
import signal
import atexit

# Load environment variables
env_vars = dotenv_values(".env")
NGROK_AUTHTOKEN = env_vars.get("NGROK_AUTHTOKEN")
ASSISTANT_NAME = env_vars.get("Assistantname", "Jarvis")

class WhatsAppBotManager:
    def __init__(self):
        self.ngrok_process = None
        self.flask_process = None
        self.ngrok_url = None
        
    def start_ngrok(self):
        """Start ngrok tunnel for WhatsApp bot"""
        print("🚀 Starting ngrok tunnel for WhatsApp bot...")
        
        try:
            # Start ngrok for port 5002
            self.ngrok_process = subprocess.Popen([
                "ngrok", "http", "5002", "--log=stdout"
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            # Wait a moment for ngrok to start
            time.sleep(3)
            
            # Get the public URL from ngrok API
            self.ngrok_url = self.get_ngrok_url()
            
            if self.ngrok_url:
                print(f"✅ Ngrok tunnel started successfully!")
                print(f"🌐 Public URL: {self.ngrok_url}")
                print(f"🔧 Ngrok Web Interface: http://localhost:4040")
                return True
            else:
                print("❌ Failed to get ngrok URL")
                return False
                
        except FileNotFoundError:
            print("❌ ngrok not found. Please install ngrok first:")
            print("   Download from: https://ngrok.com/download")
            return False
        except Exception as e:
            print(f"❌ Error starting ngrok: {e}")
            return False
    
    def get_ngrok_url(self, max_retries=10):
        """Get the public URL from ngrok API"""
        for attempt in range(max_retries):
            try:
                response = requests.get("http://localhost:4040/api/tunnels", timeout=5)
                if response.status_code == 200:
                    tunnels = response.json()["tunnels"]
                    for tunnel in tunnels:
                        if tunnel["config"]["addr"] == "http://localhost:5002":
                            return tunnel["public_url"]
                time.sleep(1)
            except requests.exceptions.RequestException:
                time.sleep(1)
                continue
        return None
    
    def start_flask_server(self):
        """Start Flask WhatsApp bot server"""
        print("🌐 Starting Flask WhatsApp bot server...")
        
        try:
            # Start Flask server
            self.flask_process = subprocess.Popen([
                sys.executable, "whatsapp_bot.py"
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            # Wait a moment for Flask to start
            time.sleep(3)
            
            # Check if Flask is running
            try:
                response = requests.get("http://localhost:5002/health", timeout=5)
                if response.status_code == 200:
                    print("✅ Flask WhatsApp bot server started successfully!")
                    print("🏥 Health check: http://localhost:5002/health")
                    return True
            except requests.exceptions.RequestException:
                pass
            
            print("⚠️  Flask server started but health check failed")
            return True
            
        except Exception as e:
            print(f"❌ Error starting Flask server: {e}")
            return False
    
    def display_setup_instructions(self):
        """Display WhatsApp setup instructions"""
        if not self.ngrok_url:
            print("❌ No ngrok URL available")
            return
        
        webhook_url = f"{self.ngrok_url}/whatsapp"
        
        print("\n" + "="*60)
        print("📱 WHATSAPP BOT SETUP INSTRUCTIONS")
        print("="*60)
        print(f"🔗 Webhook URL: {webhook_url}")
        print("="*60)
        
        print("\n📋 Step-by-Step Setup:")
        print("1. 🌐 Go to Twilio Console:")
        print("   https://console.twilio.com/us1/develop/sms/try-it-out/whatsapp-learn")
        
        print("\n2. 📱 Join WhatsApp Sandbox:")
        print("   - Send the join code to the sandbox WhatsApp number")
        print("   - Wait for confirmation message")
        
        print("\n3. ⚙️  Configure Webhook:")
        print("   - In the Twilio Console, find 'Sandbox Configuration'")
        print(f"   - Set 'When a message comes in' to: {webhook_url}")
        print("   - Set HTTP method to: POST")
        print("   - Click 'Save Configuration'")
        
        print("\n4. 🧪 Test Your Bot:")
        print(f"   - Send a message to the sandbox number")
        print(f"   - Try: 'Hello {ASSISTANT_NAME}'")
        print("   - Try: 'What's the weather today?'")
        print("   - Try: 'Tell me a joke'")
        
        print("\n🔄 This URL will remain consistent as long as this script is running!")
        print("="*60)
    
    def cleanup(self):
        """Clean up processes"""
        print("\n🧹 Cleaning up...")
        
        if self.flask_process:
            print("Stopping Flask server...")
            self.flask_process.terminate()
            try:
                self.flask_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.flask_process.kill()
        
        if self.ngrok_process:
            print("Stopping ngrok tunnel...")
            self.ngrok_process.terminate()
            try:
                self.ngrok_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.ngrok_process.kill()
        
        print("✅ Cleanup complete")
    
    def run(self):
        """Main execution method"""
        print(f"📱 {ASSISTANT_NAME} WhatsApp Bot Manager")
        print("=" * 40)
        
        # Register cleanup function
        atexit.register(self.cleanup)
        signal.signal(signal.SIGINT, lambda s, f: sys.exit(0))
        signal.signal(signal.SIGTERM, lambda s, f: sys.exit(0))
        
        # Check if ngrok auth token is configured
        if not NGROK_AUTHTOKEN:
            print("⚠️  NGROK_AUTHTOKEN not found in .env file")
            print("   Your ngrok tunnel may not work properly")
        
        # Start Flask server first
        if not self.start_flask_server():
            print("❌ Failed to start Flask server. Exiting...")
            return False
        
        # Start ngrok tunnel
        if not self.start_ngrok():
            print("❌ Failed to start ngrok. Exiting...")
            self.cleanup()
            return False
        
        # Display setup instructions
        self.display_setup_instructions()
        
        print(f"\n🎉 {ASSISTANT_NAME} WhatsApp Bot is running!")
        print("📊 Monitor ngrok traffic at: http://localhost:4040")
        print("🏥 Bot health check at: http://localhost:5002/health")
        print("🧪 Test endpoint at: http://localhost:5002/test")
        print("\n⚠️  Press Ctrl+C to stop the bot")
        
        try:
            # Keep the script running
            while True:
                time.sleep(1)
                
                # Check if processes are still running
                if self.ngrok_process and self.ngrok_process.poll() is not None:
                    print("⚠️  Ngrok process died. Restarting...")
                    self.start_ngrok()
                
                if self.flask_process and self.flask_process.poll() is not None:
                    print("⚠️  Flask process died. Restarting...")
                    self.start_flask_server()
                    
        except KeyboardInterrupt:
            print("\n👋 Shutting down WhatsApp bot...")
            return True

def main():
    """Main function"""
    # Change to script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    # Check if required files exist
    required_files = ["whatsapp_bot.py", ".env"]
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"❌ Missing required files: {', '.join(missing_files)}")
        return False
    
    # Check if Gemini API key is configured
    env_vars = dotenv_values(".env")
    if not env_vars.get("GEMINI_API_KEY"):
        print("⚠️  GEMINI_API_KEY not found in .env file")
        print("   The bot will try to use JARVIS modules only")
    
    # Start the bot manager
    manager = WhatsAppBotManager()
    return manager.run()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
