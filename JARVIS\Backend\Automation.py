from AppOpener import close, open as appopen
from webbrowser import open as webopen
from pywhatkit.misc import search, playonyt
from dotenv import dotenv_values
from bs4 import BeautifulSoup
from rich import print
from groq import Groq
import webbrowser
import subprocess
import requests
import keyboard
import asyncio
import os

env_vars = dotenv_values(".env")
GroqAPIKey = env_vars.get("GroqAPIKey")

classes = ["zCubwf", "hgKElc", "LTKOO sY7ric", "Z0LcW", "gsrt vk_bk FzvWSb YwPhnf", "pclqee", "tw-Data-text tw-text-small tw-ta", 
           "IZ6rdc", "O5uR6d LTKOO", "vlzY6d", "webanswers-webanswers_table__webanswers-table", "dDoNo ikb4Bb gsrt", "sXLa0e", 
           "LWkfKe", "VQF4g", "qv3Wpe", "kno-rdesc", "SPZz6b"]

useragent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.142.86 Safari/537.36'

client = Groq(api_key=GroqAPIKey)

professional_responses = [
    "Your satisfaction is my top priority; feel free to reach out if there's anything else I can help you with.",
    "I'm at your server for any additional questions or support you may need-don't hesitate to ask."
]

messages = []

SystemChatBot = [{"role": "system", "content": f"Hello,Ia am {os.environ['Username']}, You're a content writer. You have to write content like letters, codes, applications, essays, notes, songs, poems, etc."}]

def GoogleSearch(Topic):
    search(Topic)
    return True

def Content(topic):

    def OpenNotepad(File):
        default_text_editor = 'notepad.exe'
        subprocess.Popen([default_text_editor, File])

    def ContentWriterAI(prompt):
        from typing import cast, Dict, Any

        user_message: Dict[str, Any] = {"role": "user", "content": prompt}
        messages.append(user_message)

        # Create properly typed messages list
        all_messages = SystemChatBot + messages

        completion = client.chat.completions.create(
            model="mixtral-8x7b-32768",
            messages=cast(Any, all_messages),
            max_tokens=2048,
            temperature=0.7,
            top_p=1,
            stream=True,
            stop=None,
        )

        Answer = ""

        for chunk in completion:
            if chunk.choices[0].delta.content:
                Answer += chunk.choices[0].delta.content

        Answer = Answer.replace("</s>", "")
        assistant_message = {"role": "assistant", "content": Answer}
        messages.append(assistant_message)
        return Answer

    cleaned_topic = topic.replace("Content ", "")
    ContentByAI = ContentWriterAI(cleaned_topic)

    with open(rf"Data\{cleaned_topic.lower().replace(' ', '')}.txt", "w", encoding='utf-8') as file:
        file.write(ContentByAI)
        file.close()

    OpenNotepad(rf"Data\{cleaned_topic.lower().replace(' ', '')}.txt")
    return True

def YoutubeSearch(Topic):
    Url4Search = f"https://www.youtube.com/results?search_query={Topic}"
    webbrowser.open(Url4Search)
    return True

def PlayYoutube(query):
    playonyt(query)
    return True

def OpenApp(app, sess=requests.session()):

    # Check if it's a common website/service and handle accordingly
    app_lower = app.lower().strip()

    # Direct website mappings for common services
    website_mappings = {
        'google': 'https://www.google.com',
        'youtube': 'https://www.youtube.com',
        'facebook': 'https://www.facebook.com',
        'twitter': 'https://www.twitter.com',
        'instagram': 'https://www.instagram.com',
        'linkedin': 'https://www.linkedin.com',
        'github': 'https://www.github.com',
        'stackoverflow': 'https://www.stackoverflow.com',
        'reddit': 'https://www.reddit.com',
        'amazon': 'https://www.amazon.com',
        'netflix': 'https://www.netflix.com',
        'spotify': 'https://www.spotify.com',
        'gmail': 'https://www.gmail.com',
        'outlook': 'https://www.outlook.com',
        'whatsapp': 'https://web.whatsapp.com',
        'discord': 'https://discord.com',
        'slack': 'https://slack.com',
        'zoom': 'https://zoom.us',
        'teams': 'https://teams.microsoft.com',
        'dropbox': 'https://www.dropbox.com',
        'drive': 'https://drive.google.com',
        'onedrive': 'https://onedrive.live.com',
        'telegram' : 'https://web.telegram.org/k/'
    }

    # Apps that should prioritize web version over local apps
    web_priority_apps = {
        'telegram', 'whatsapp', 'discord', 'slack', 'teams',
        'gmail', 'outlook', 'drive', 'onedrive', 'dropbox',
        'facebook', 'twitter', 'instagram', 'linkedin', 'reddit'
    }

    # Check for direct website match first for web-priority apps
    if app_lower in web_priority_apps and app_lower in website_mappings:
        print(f"Opening {app} web version...")
        webopen(website_mappings[app_lower])
        return True

    # Check for partial matches in web-priority apps
    for site_name in web_priority_apps:
        if site_name in app_lower or app_lower in site_name:
            if site_name in website_mappings:
                print(f"Found web service: {site_name}. Opening web version...")
                webopen(website_mappings[site_name])
                return True

    # Try to open local app for non-web-priority apps
    try:
        appopen(app, match_closest=True, output=True, throw_error=True)
        return True

    except:
        print(f"App '{app}' not found locally. Searching online...")

        # Check for direct website match for remaining apps
        if app_lower in website_mappings:
            print(f"Opening {app} website directly...")
            webopen(website_mappings[app_lower])
            return True

        # Check for partial matches in remaining website names
        for site_name, url in website_mappings.items():
            if site_name not in web_priority_apps:  # Skip already checked web-priority apps
                if site_name in app_lower or app_lower in site_name:
                    print(f"Found similar service: {site_name}. Opening website...")
                    webopen(url)
                    return True

        # Check for direct website match
        if app_lower in website_mappings:
            print(f"Opening {app} website directly...")
            webopen(website_mappings[app_lower])
            return True

        # Check for partial matches in website names
        for site_name, url in website_mappings.items():
            if site_name in app_lower or app_lower in site_name:
                print(f"Found similar service: {site_name}. Opening website...")
                webopen(url)
                return True

        def extract_links(html):
            if html is None:
                return []
            soup = BeautifulSoup(html, 'html.parser')
            links = soup.find_all('a', {'jsname': 'UWckNb'})
            hrefs = []
            for link in links:
                try:
                    # Suppress type checking - this works at runtime
                    href = link.get('href')  # type: ignore
                    if href and isinstance(href, str):
                        hrefs.append(href)
                except (AttributeError, TypeError):
                    continue
            return hrefs

        def search_google(query):
            url = f"https://www.google.com/search?q={query}"
            headers = {"User-Agent": useragent}
            response = sess.get(url, headers=headers)

            if response.status_code == 200:
                return response.text
            else:
                print("Failed to retrieve search results.")
                return None

        # If no direct match found, search Google for the app/service
        print(f"Searching Google for '{app}'...")
        html = search_google(f"{app} official website")

        if html:
            links = extract_links(html)
            if links and links[0]:
                print(f"Opening search result for '{app}'...")
                webopen(str(links[0]))
            else:
                # Fallback: just search Google for the term
                print(f"No specific results found. Opening Google search for '{app}'...")
                webopen(f"https://www.google.com/search?q={app}")
        else:
            # Final fallback: direct Google search
            print(f"Search failed. Opening Google search for '{app}'...")
            webopen(f"https://www.google.com/search?q={app}")

        return True
    
def CloseApp(app):

    if "edge" in app:
        pass
    else:
        try:
            close(app, match_closest=True, output=True, throw_error=True)
            return True
        except:
            return False
        
def System(command):

    def mute():
        keyboard.press_and_release("volume mute")

    def unmute():
        keyboard.press_and_release("volume unmute")

    def volumeup():
        keyboard.press_and_release("volume up")

    def volumedown():
        keyboard.press_and_release("volume down")

    if command == "mute":
        mute()
    elif command == "unmute":
        unmute()
    elif command == "volume up":
        volumeup()
    elif command == "volume down":
        volumedown()
    
    return True

async def TranslateAndExecule(commands: list[str]):

    funcs = []

    for command in commands:

        if command.startswith("open "):

            if "open it" in command:
                pass
            
            if "open file" == command:
                pass
            
            else:
                fun = asyncio.to_thread(OpenApp, command.removeprefix("open "))
                funcs.append(fun)

        elif command.startswith("general "):
            pass

        elif command.startswith("realtime "):
            pass

        elif command.startswith("close "):
            fun = asyncio.to_thread(CloseApp, command.removeprefix("close "))
            funcs.append(fun)

        elif command.startswith("play "):
            fun = asyncio.to_thread(PlayYoutube, command.removeprefix("play "))
            funcs.append(fun)

        elif command.startswith("content "):
            fun = asyncio.to_thread(Content, command.removeprefix("content "))
            funcs.append(fun)

        elif command.startswith("google search "):
            fun = asyncio.to_thread(GoogleSearch, command.removeprefix("google search "))
            funcs.append(fun)

        elif command.startswith("youtube search "):
            fun = asyncio.to_thread(YoutubeSearch, command.removeprefix("youtube search "))
            funcs.append(fun)

        elif command.startswith("system "):
            fun = asyncio.to_thread(System, command.removeprefix("system "))
            funcs.append(fun)

        else:
            print(f"No Function Found. For {command}")

    results = await asyncio.gather(*funcs)
    
    for result in results:
        if isinstance(result, str):
            yield result
        else:
            yield result

async def Automation(commands: list[str]):

    async for result in TranslateAndExecule(commands):
        pass
    
    return True
