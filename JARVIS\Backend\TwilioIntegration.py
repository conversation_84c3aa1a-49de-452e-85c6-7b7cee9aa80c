"""
Twilio Integration Module for JARVIS AI Assistant
Handles voice and SMS processing with advanced features
"""

import os
import sys
import tempfile
import requests
import logging
import time
from typing import Optional, Dict, Any
from dotenv import dotenv_values
import asyncio
import edge_tts
import pygame
from twilio.rest import Client
from twilio.twiml.voice_response import VoiceResponse
from twilio.twiml.messaging_response import MessagingResponse

# Add parent directory to path for JARVIS imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from Backend.Chatbot import ChatBot
    from Backend.RealtimeSearchEngine import RealtimeSearchEngine
    from Backend.Model import FirstLayerDMM
    from Frontend.GUI import QueryModifier
except ImportError as e:
    logging.warning(f"Could not import JARVIS modules: {e}")

# Load environment variables
env_vars = dotenv_values(".env")

class TwilioVoiceProcessor:
    """Handles Twilio voice call processing with TTS capabilities"""
    
    def __init__(self):
        self.twilio_client = None
        self.assistant_voice = env_vars.get("AssistantVoice", "en-CA-LiamNeural")
        self.assistant_name = env_vars.get("Assistantname", "Jarvis")
        self.username = env_vars.get("Username", "User")
        
        # Initialize Twilio client
        account_sid = env_vars.get("TWILIO_ACCOUNT_SID")
        auth_token = env_vars.get("TWILIO_AUTH_TOKEN")
        
        if account_sid and auth_token:
            self.twilio_client = Client(account_sid, auth_token)
            logging.info("Twilio client initialized successfully")
        else:
            logging.warning("Twilio credentials not found")
    
    async def generate_tts_audio(self, text: str, output_path: str) -> bool:
        """Generate TTS audio file using edge-tts"""
        try:
            logging.info(f"Generating TTS for: {text[:50]}...")
            communicate = edge_tts.Communicate(text, self.assistant_voice, pitch='+5Hz', rate='+13%')
            await communicate.save(output_path)
            logging.info(f"TTS audio saved to: {output_path}")
            return True
        except Exception as e:
            logging.error(f"Error generating TTS: {e}")
            return False
    
    def create_voice_response_with_tts(self, text: str, callback_url: str = None) -> VoiceResponse:
        """Create a Twilio VoiceResponse with custom TTS"""
        response = VoiceResponse()
        
        try:
            # For now, use Twilio's built-in TTS
            # In future versions, we can serve custom TTS files
            response.say(text, voice='alice', rate='medium')
            
            if callback_url:
                response.redirect(callback_url)
                
        except Exception as e:
            logging.error(f"Error creating voice response: {e}")
            response.say("I'm sorry, I encountered an error. Please try again.", voice='alice')
        
        return response
    
    def create_interactive_voice_menu(self) -> VoiceResponse:
        """Create an interactive voice menu for JARVIS"""
        response = VoiceResponse()
        
        welcome_text = f"""Hello! You've reached {self.assistant_name}, your AI assistant. 
        I can help you with questions, searches, and various tasks. 
        Please speak your question clearly after the beep, or press 1 to hear more options."""
        
        gather = response.gather(
            input='speech dtmf',
            action='/webhook/voice/process',
            method='POST',
            speech_timeout='auto',
            timeout=10,
            num_digits=1
        )
        
        gather.say(welcome_text, voice='alice')
        
        # Fallback if no input
        response.say("I didn't receive your input. Please try calling again.", voice='alice')
        
        return response
    
    def create_options_menu(self) -> VoiceResponse:
        """Create options menu for voice interface"""
        response = VoiceResponse()
        
        options_text = """Here are your options:
        Press 1 to ask a general question,
        Press 2 for real-time information search,
        Press 3 to hear this menu again,
        Or simply speak your question at any time."""
        
        gather = response.gather(
            input='dtmf speech',
            action='/webhook/voice/handle_option',
            method='POST',
            num_digits=1,
            timeout=10
        )
        
        gather.say(options_text, voice='alice')
        
        # Default action
        response.redirect('/webhook/voice')
        
        return response

class TwilioSMSProcessor:
    """Handles SMS message processing and responses"""
    
    def __init__(self):
        self.assistant_name = env_vars.get("Assistantname", "Jarvis")
        self.max_sms_length = 1600  # Twilio SMS limit
        
    def process_sms_message(self, message_body: str, sender_number: str) -> str:
        """Process SMS message through JARVIS and return response"""
        try:
            logging.info(f"Processing SMS from {sender_number}: {message_body}")
            
            # Use JARVIS decision making
            decision = FirstLayerDMM(message_body)
            logging.info(f"Decision: {decision}")
            
            # Process based on decision
            general_queries = [i for i in decision if i.startswith("general")]
            realtime_queries = [i for i in decision if i.startswith("realtime")]
            
            if realtime_queries or (general_queries and realtime_queries):
                # Handle realtime search
                merged_query = " and ".join([" ".join(i.split()[1:]) for i in decision 
                                           if i.startswith("general") or i.startswith("realtime")])
                response = RealtimeSearchEngine(QueryModifier(merged_query))
            elif general_queries:
                # Handle general chat
                query_final = general_queries[0].replace("general ", "")
                response = ChatBot(QueryModifier(query_final))
            else:
                # Default to chatbot
                response = ChatBot(QueryModifier(message_body))
            
            # Truncate response if too long for SMS
            if len(response) > self.max_sms_length:
                response = response[:self.max_sms_length-50] + "... (response truncated)"
            
            logging.info(f"Generated SMS response: {response[:100]}...")
            return response
            
        except Exception as e:
            logging.error(f"Error processing SMS: {e}")
            return "I'm sorry, I encountered an error processing your message. Please try again."
    
    def create_sms_response(self, message_body: str, sender_number: str) -> MessagingResponse:
        """Create Twilio MessagingResponse"""
        response = MessagingResponse()
        
        if not message_body.strip():
            welcome_msg = f"Hello! I'm {self.assistant_name}, your AI assistant. How can I help you today?"
            response.message(welcome_msg)
        else:
            jarvis_response = self.process_sms_message(message_body, sender_number)
            response.message(f"{self.assistant_name}: {jarvis_response}")
        
        return response

class TwilioCallManager:
    """Manages active calls and call state"""
    
    def __init__(self):
        self.active_calls: Dict[str, Dict[str, Any]] = {}
        self.twilio_client = None
        
        # Initialize Twilio client
        account_sid = env_vars.get("TWILIO_ACCOUNT_SID")
        auth_token = env_vars.get("TWILIO_AUTH_TOKEN")
        
        if account_sid and auth_token:
            self.twilio_client = Client(account_sid, auth_token)
    
    def start_call_session(self, call_sid: str, caller_number: str):
        """Start a new call session"""
        self.active_calls[call_sid] = {
            'caller_number': caller_number,
            'start_time': time.time(),
            'messages': [],
            'state': 'active'
        }
        logging.info(f"Started call session: {call_sid}")
    
    def end_call_session(self, call_sid: str):
        """End a call session"""
        if call_sid in self.active_calls:
            self.active_calls[call_sid]['state'] = 'ended'
            logging.info(f"Ended call session: {call_sid}")
    
    def add_message_to_call(self, call_sid: str, message: str, message_type: str = 'user'):
        """Add a message to the call session"""
        if call_sid in self.active_calls:
            self.active_calls[call_sid]['messages'].append({
                'type': message_type,
                'content': message,
                'timestamp': time.time()
            })
    
    def get_call_context(self, call_sid: str) -> Optional[Dict[str, Any]]:
        """Get call context for processing"""
        return self.active_calls.get(call_sid)

# Global instances
voice_processor = TwilioVoiceProcessor()
sms_processor = TwilioSMSProcessor()
call_manager = TwilioCallManager()

def create_welcome_voice_response() -> VoiceResponse:
    """Create welcome voice response"""
    return voice_processor.create_interactive_voice_menu()

def create_options_voice_response() -> VoiceResponse:
    """Create options voice response"""
    return voice_processor.create_options_menu()

def process_voice_input(speech_result: str = None, digits: str = None, call_sid: str = None) -> VoiceResponse:
    """Process voice input and return appropriate response"""
    response = VoiceResponse()
    
    try:
        if digits:
            # Handle DTMF input
            if digits == '1':
                response.say("Please ask your general question now.", voice='alice')
                response.record(action='/webhook/voice/process_question', max_length=30)
            elif digits == '2':
                response.say("Please ask your search question now.", voice='alice')
                response.record(action='/webhook/voice/process_search', max_length=30)
            elif digits == '3':
                return voice_processor.create_options_menu()
            else:
                response.say("Invalid option. Please try again.", voice='alice')
                response.redirect('/webhook/voice/options')
        
        elif speech_result:
            # Process speech input through JARVIS
            jarvis_response = sms_processor.process_sms_message(speech_result, "voice_call")
            
            # Create voice response
            response.say(f"Here's what I found: {jarvis_response}", voice='alice')
            response.say("Would you like to ask another question? Press 1 for yes, or hang up to end the call.", voice='alice')
            
            gather = response.gather(num_digits=1, action='/webhook/voice/continue')
            gather.say("Press 1 to continue or hang up to end the call.", voice='alice')
        
        else:
            response.say("I didn't understand your input. Please try again.", voice='alice')
            response.redirect('/webhook/voice')
    
    except Exception as e:
        logging.error(f"Error processing voice input: {e}")
        response.say("I'm sorry, I encountered an error. Please try calling again.", voice='alice')
    
    return response

def process_sms_message(message_body: str, sender_number: str) -> MessagingResponse:
    """Process SMS message and return response"""
    return sms_processor.create_sms_response(message_body, sender_number)
