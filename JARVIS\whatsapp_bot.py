#!/usr/bin/env python3
"""
JARVIS WhatsApp Bot using Twilio WhatsApp Sandbox
Integrates with existing JARVIS functionality and Gemini API
"""

import os
import sys
from flask import Flask, request
from twilio.twiml.messaging_response import MessagingResponse
import google.generativeai as genai
from dotenv import load_dotenv
import logging

# Add the current directory to Python path for JARVIS imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import JARVIS modules
try:
    from Backend.Chatbot import ChatBot
    from Backend.RealtimeSearchEngine import RealtimeSearchEngine
    from Backend.Model import FirstLayerDMM
    from Frontend.GUI import QueryModifier
    JARVIS_AVAILABLE = True
    print("✅ JARVIS modules loaded successfully")
except ImportError as e:
    print(f"⚠️  JARVIS modules not available: {e}")
    print("Running in Gemini-only mode...")
    JARVIS_AVAILABLE = False

# Load environment variables from .env file
load_dotenv()

# Initialize the Flask app
app = Flask(__name__)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Get configuration from environment
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
ASSISTANT_NAME = os.getenv("Assistantname", "Jarvis")
USERNAME = os.getenv("Username", "User")

# Configure the Gemini API client
gemini_model = None
if GEMINI_API_KEY:
    try:
        genai.configure(api_key=GEMINI_API_KEY)
        gemini_model = genai.GenerativeModel('gemini-2.0-flash-exp')
        print("✅ Gemini API configured successfully")
    except Exception as e:
        print(f"❌ Error configuring Gemini API: {e}")
        gemini_model = None
else:
    print("⚠️  GEMINI_API_KEY not found in .env file")

class WhatsAppProcessor:
    """Handles WhatsApp message processing with multiple AI backends"""
    
    def __init__(self):
        self.max_message_length = 1600  # WhatsApp message limit
        self.functions = ["open", "close", "play", "system", "content", "google search", "youtube search"]
    
    def process_with_jarvis(self, message):
        """Process message using JARVIS AI system"""
        try:
            logger.info(f"Processing with JARVIS: {message}")
            
            # Use JARVIS decision making
            decision = FirstLayerDMM(message)
            logger.info(f"JARVIS Decision: {decision}")
            
            # Check for general and realtime queries
            general_queries = [i for i in decision if i.startswith("general")]
            realtime_queries = [i for i in decision if i.startswith("realtime")]
            
            if realtime_queries or (general_queries and realtime_queries):
                # Handle realtime search
                merged_query = " and ".join([" ".join(i.split()[1:]) for i in decision 
                                           if i.startswith("general") or i.startswith("realtime")])
                response = RealtimeSearchEngine(QueryModifier(merged_query))
            elif general_queries:
                # Handle general chat
                query_final = general_queries[0].replace("general ", "")
                response = ChatBot(QueryModifier(query_final))
            else:
                # Default to chatbot
                response = ChatBot(QueryModifier(message))
            
            return response
            
        except Exception as e:
            logger.error(f"Error processing with JARVIS: {e}")
            return None
    
    def process_with_gemini(self, message):
        """Process message using Gemini API"""
        try:
            if not gemini_model:
                return None
                
            logger.info(f"Processing with Gemini: {message}")
            
            # Create a context-aware prompt
            prompt = f"""You are {ASSISTANT_NAME}, an AI assistant. The user's name is {USERNAME}.
            
User message: {message}

Please respond as {ASSISTANT_NAME} would - be helpful, intelligent, and conversational. Keep responses concise for WhatsApp."""
            
            gemini_response = gemini_model.generate_content(prompt)
            response = gemini_response.text
            
            logger.info(f"Gemini response: {response[:100]}...")
            return response
            
        except Exception as e:
            logger.error(f"Error processing with Gemini: {e}")
            return None
    
    def process_message(self, message, sender_number):
        """Process message with fallback between JARVIS and Gemini"""
        logger.info(f"WhatsApp message from {sender_number}: {message}")
        
        # Try JARVIS first if available
        if JARVIS_AVAILABLE:
            response = self.process_with_jarvis(message)
            if response:
                # Truncate if too long
                if len(response) > self.max_message_length:
                    response = response[:self.max_message_length-50] + "... (truncated)"
                return f"{ASSISTANT_NAME}: {response}"
        
        # Fallback to Gemini
        response = self.process_with_gemini(message)
        if response:
            # Truncate if too long
            if len(response) > self.max_message_length:
                response = response[:self.max_message_length-50] + "... (truncated)"
            return response
        
        # Final fallback
        return f"Sorry, I'm having trouble processing your message right now. Please try again."

# Initialize processor
processor = WhatsAppProcessor()

@app.route("/")
def index():
    """Root endpoint with status information"""
    status = {
        "service": f"{ASSISTANT_NAME} WhatsApp Bot",
        "jarvis_available": JARVIS_AVAILABLE,
        "gemini_available": gemini_model is not None,
        "endpoints": {
            "whatsapp": "/whatsapp",
            "health": "/health"
        }
    }
    
    html = f"""
    <h1>🤖 {ASSISTANT_NAME} WhatsApp Bot</h1>
    <p><strong>Status:</strong> Running ✅</p>
    <p><strong>JARVIS Integration:</strong> {'✅ Available' if JARVIS_AVAILABLE else '❌ Not Available'}</p>
    <p><strong>Gemini API:</strong> {'✅ Available' if gemini_model else '❌ Not Available'}</p>
    
    <h2>📱 Setup Instructions:</h2>
    <ol>
        <li>Go to <a href="https://console.twilio.com/us1/develop/sms/try-it-out/whatsapp-learn" target="_blank">Twilio WhatsApp Sandbox</a></li>
        <li>Send the join code to the sandbox number</li>
        <li>Set webhook URL to: <code>https://your-ngrok-url.ngrok.io/whatsapp</code></li>
        <li>Start chatting with {ASSISTANT_NAME}!</li>
    </ol>
    
    <h2>🔗 Endpoints:</h2>
    <ul>
        <li><strong>/whatsapp</strong> - WhatsApp webhook endpoint</li>
        <li><strong>/health</strong> - Health check</li>
    </ul>
    """
    return html

@app.route("/whatsapp", methods=['POST'])
def whatsapp_reply():
    """
    WhatsApp webhook endpoint
    Receives messages from Twilio WhatsApp sandbox and responds with AI
    """
    # Get the message from the POST request
    incoming_msg = request.values.get('Body', '').strip()
    sender_number = request.values.get('From', '')
    
    # Create a Twilio response object
    resp = MessagingResponse()
    
    if not incoming_msg:
        welcome_msg = f"Hello! I'm {ASSISTANT_NAME}, your AI assistant. How can I help you today?"
        resp.message(welcome_msg)
    else:
        # Process the message
        reply_text = processor.process_message(incoming_msg, sender_number)
        resp.message(reply_text)
    
    return str(resp)

@app.route("/health")
def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": f"{ASSISTANT_NAME} WhatsApp Bot",
        "jarvis_available": JARVIS_AVAILABLE,
        "gemini_available": gemini_model is not None
    }

@app.route("/test")
def test_endpoint():
    """Test endpoint to verify AI functionality"""
    test_message = "Hello, how are you?"
    response = processor.process_message(test_message, "test_user")
    return {
        "test_message": test_message,
        "ai_response": response,
        "jarvis_available": JARVIS_AVAILABLE,
        "gemini_available": gemini_model is not None
    }

if __name__ == "__main__":
    print(f"🤖 Starting {ASSISTANT_NAME} WhatsApp Bot...")
    print("=" * 50)

    # Check configuration
    if not JARVIS_AVAILABLE and not gemini_model:
        print("⚠️  Warning: Neither JARVIS nor Gemini is available!")
        print("   The bot will use basic fallback responses.")

    if JARVIS_AVAILABLE:
        print("✅ JARVIS AI system loaded")

    if gemini_model:
        print("✅ Gemini API configured")

    print("\n📱 WhatsApp Setup Instructions:")
    print("1. Start ngrok: ngrok http 5002")
    print("2. Go to Twilio WhatsApp Sandbox")
    print("3. Set webhook URL to: https://your-ngrok-url.ngrok.io/whatsapp")
    print("4. Send join code to sandbox number")
    print("5. Start chatting!")

    print(f"\n🌐 Server starting on http://localhost:5002")
    print("🔧 Health check: http://localhost:5002/health")

    try:
        # Run the Flask app with error handling
        app.run(debug=False, port=5002, host='0.0.0.0', use_reloader=False)
    except Exception as e:
        print(f"❌ Error starting Flask server: {e}")
        sys.exit(1)
