# ngrok configuration for JARVIS Twilio integration
# This configuration uses your existing auth token for a consistent tunnel

version: "2"
authtoken: 31a7ZUS6t0u6f1fiv3Ql9hkvIG1_5sZhpsSxHPcujjvnFbLDF

tunnels:
  jarvis-twilio:
    proto: http
    addr: 5000
    # Uncomment the line below if you have a reserved domain
    # hostname: your-reserved-domain.ngrok.io
    inspect: true
    bind_tls: true
    # Custom subdomain (requires paid plan)
    # subdomain: jarvis-ai

  jarvis-whatsapp:
    proto: http
    addr: 5002
    # Uncomment the line below if you have a reserved domain for WhatsApp
    # hostname: jarvis-whatsapp.ngrok.io
    inspect: true
    bind_tls: true
    # Custom subdomain (requires paid plan)
    # subdomain: jarvis-whatsapp
    
  # Optional: Additional tunnel for development
  jarvis-dev:
    proto: http
    addr: 5001
    inspect: false

# Web interface configuration
web_addr: localhost:4040
console_ui: true

# Logging
log_level: info
log_format: logfmt
log: ngrok.log
