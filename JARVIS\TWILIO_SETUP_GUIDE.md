# JARVIS Twilio Integration Setup Guide

This guide will help you set up Twilio integration with your JARVIS AI assistant for production deployment using ngrok.

## 🎯 What This Setup Provides

- **Voice Calls**: Call your Twilio number and speak to JARVIS
- **SMS Messages**: Send text messages to JARVIS and get AI responses
- **Production-Ready**: Consistent webhook URLs that don't change
- **Easy Management**: Single script to start everything

## 📋 Prerequisites

1. **Python 3.7+** installed on your system
2. **Twilio Account** (free tier works fine)
3. **ngrok** installed on your system
4. **Twilio Phone Number** (can be purchased from Twilio Console)

## 🚀 Quick Setup Steps

### Step 1: Install ngrok
1. Download ngrok from: https://ngrok.com/download
2. Extract and place `ngrok.exe` in your system PATH, or in the JARVIS folder
3. Your ngrok auth token is already configured in the `.env` file

### Step 2: Get Twilio Credentials
1. Go to [Twi<PERSON> Console](https://console.twilio.com/)
2. Sign up for a free account if you don't have one
3. From the Dashboard, copy:
   - **Account SID**
   - **Auth Token**
4. Purchase a phone number:
   - Go to Phone Numbers > Manage > Buy a number
   - Choose a number with Voice and SMS capabilities
   - Note down the phone number (format: +**********)

### Step 3: Configure Environment Variables
Edit your `.env` file and replace the placeholder values:

```env
# Replace these with your actual Twilio credentials
TWILIO_ACCOUNT_SID=your_actual_account_sid_here
TWILIO_AUTH_TOKEN=your_actual_auth_token_here
TWILIO_PHONE_NUMBER=+**********
```

### Step 4: Install Dependencies
Run one of these commands in the JARVIS directory:

```bash
# Option 1: Using pip
pip install -r Requirements.txt

# Option 2: Using the batch file (Windows)
start_twilio.bat
```

### Step 5: Start the Server
Run the startup script:

```bash
# Option 1: Python script
python start_twilio_server.py

# Option 2: Batch file (Windows)
start_twilio.bat
```

The script will:
- Start ngrok tunnel
- Start Flask webhook server
- Display webhook URLs for Twilio configuration

### Step 6: Configure Twilio Webhooks
1. Go to [Twilio Console](https://console.twilio.com/)
2. Navigate to: Phone Numbers > Manage > Active numbers
3. Click on your Twilio phone number
4. Configure webhooks using the URLs displayed by the startup script:
   - **Voice Webhook**: `https://your-ngrok-url.ngrok.io/webhook/voice`
   - **SMS Webhook**: `https://your-ngrok-url.ngrok.io/webhook/sms`
5. Set HTTP method to **POST** for both
6. Click **Save configuration**

## 🎉 Testing Your Setup

### Test SMS
1. Send a text message to your Twilio phone number
2. Ask JARVIS a question like: "What's the weather today?"
3. You should receive an AI-generated response

### Test Voice Calls
1. Call your Twilio phone number
2. Listen to the welcome message
3. Speak your question after the beep
4. Currently, voice responses are sent via SMS (speech-to-text processing is in development)

## 🔧 Advanced Configuration

### Reserved Domain (Recommended for Production)
To get a consistent ngrok URL that never changes:

1. Upgrade to ngrok Pro plan
2. Reserve a domain in ngrok dashboard
3. Edit `ngrok.yml` and uncomment the hostname line:
   ```yaml
   hostname: your-reserved-domain.ngrok.io
   ```

### Custom Subdomain
If you have ngrok Pro, you can use a custom subdomain:
```yaml
subdomain: jarvis-ai
```

## 📊 Monitoring and Debugging

### Ngrok Web Interface
- Access: http://localhost:4040
- View all HTTP requests and responses
- Inspect webhook calls from Twilio

### Flask Health Check
- Access: http://localhost:5000/health
- Verify server is running properly

### Log Files
- ngrok logs: `ngrok.log`
- Flask logs: Console output

## 🛠️ Troubleshooting

### Common Issues

**1. "ngrok not found" error**
- Install ngrok from https://ngrok.com/download
- Add ngrok to your system PATH

**2. "Twilio credentials not found"**
- Check your `.env` file has correct Twilio credentials
- Ensure no extra spaces around the values

**3. "Webhook not receiving calls"**
- Verify webhook URLs in Twilio Console
- Check ngrok tunnel is active (http://localhost:4040)
- Ensure Flask server is running (http://localhost:5000/health)

**4. "JARVIS modules not found"**
- Ensure you're running from the JARVIS directory
- Check all Backend modules are present

### Getting Help
- Check ngrok status: http://localhost:4040
- Check Flask health: http://localhost:5000/health
- Review console output for error messages

## 🔒 Security Notes

- Keep your Twilio credentials secure
- Don't commit `.env` file to version control
- Consider using Twilio's webhook signature validation for production
- Monitor usage to avoid unexpected charges

## 📱 Usage Examples

### SMS Commands
- "What's the weather in New York?"
- "Tell me a joke"
- "Search for Python tutorials"
- "What's the latest news?"

### Voice Commands
- Call the number and speak naturally
- JARVIS will process your speech and respond via SMS
- Future updates will include voice responses

## 🚀 Production Deployment

For true production deployment:
1. Use a cloud server (AWS, Google Cloud, Azure)
2. Set up proper SSL certificates
3. Use a process manager like PM2 or systemd
4. Implement proper logging and monitoring
5. Set up database for conversation history

---

**🎉 Congratulations!** Your JARVIS AI assistant is now accessible via phone calls and SMS messages with a production-ready setup that maintains consistent webhook URLs.
