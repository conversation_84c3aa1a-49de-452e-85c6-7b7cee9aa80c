#!/usr/bin/env python3
"""
Interactive Twilio configuration script for JARVIS
Helps set up Twilio credentials and test the connection
"""

import os
import sys
from dotenv import dotenv_values
import re

def get_current_env_vars():
    """Get current environment variables"""
    return dotenv_values(".env")

def update_env_file(updates):
    """Update .env file with new values"""
    # Read current .env file
    with open(".env", "r") as f:
        lines = f.readlines()
    
    # Update lines
    updated_lines = []
    updated_keys = set()
    
    for line in lines:
        line = line.strip()
        if "=" in line and not line.startswith("#"):
            key = line.split("=")[0].strip()
            if key in updates:
                updated_lines.append(f"{key}={updates[key]}\n")
                updated_keys.add(key)
            else:
                updated_lines.append(line + "\n")
        else:
            updated_lines.append(line + "\n")
    
    # Add any new keys that weren't in the file
    for key, value in updates.items():
        if key not in updated_keys:
            updated_lines.append(f"{key}={value}\n")
    
    # Write back to file
    with open(".env", "w") as f:
        f.writelines(updated_lines)

def validate_twilio_credentials(account_sid, auth_token):
    """Validate Twilio credentials"""
    try:
        from twilio.rest import Client
        client = Client(account_sid, auth_token)
        account = client.api.accounts(account_sid).fetch()
        return True, account.friendly_name
    except Exception as e:
        return False, str(e)

def get_twilio_phone_numbers(account_sid, auth_token):
    """Get available Twilio phone numbers"""
    try:
        from twilio.rest import Client
        client = Client(account_sid, auth_token)
        phone_numbers = client.incoming_phone_numbers.list()
        return [pn.phone_number for pn in phone_numbers]
    except Exception as e:
        print(f"Error fetching phone numbers: {e}")
        return []

def main():
    """Main configuration function"""
    print("🤖 JARVIS Twilio Configuration Setup")
    print("=" * 50)
    
    # Check if .env file exists
    if not os.path.exists(".env"):
        print("❌ .env file not found!")
        return False
    
    current_env = get_current_env_vars()
    updates = {}
    
    print("\n📋 Current Twilio Configuration:")
    twilio_vars = ["TWILIO_ACCOUNT_SID", "TWILIO_AUTH_TOKEN", "TWILIO_PHONE_NUMBER"]
    
    for var in twilio_vars:
        current_value = current_env.get(var, "Not set")
        if current_value.startswith("your_"):
            current_value = "Not configured"
        elif len(current_value) > 10:
            current_value = current_value[:6] + "..." + current_value[-4:]
        print(f"  {var}: {current_value}")
    
    print("\n🔧 Let's configure your Twilio credentials!")
    print("\nTo get your credentials:")
    print("1. Go to https://console.twilio.com/")
    print("2. Sign up for a free account if you don't have one")
    print("3. From the Dashboard, copy your Account SID and Auth Token")
    print("4. Purchase a phone number with Voice and SMS capabilities")
    
    # Get Account SID
    print("\n" + "-" * 30)
    current_sid = current_env.get("TWILIO_ACCOUNT_SID", "")
    if current_sid and not current_sid.startswith("your_"):
        print(f"Current Account SID: {current_sid[:6]}...{current_sid[-4:]}")
        use_current = input("Use current Account SID? (y/n): ").lower().strip()
        if use_current != 'y':
            current_sid = ""
    
    if not current_sid or current_sid.startswith("your_"):
        while True:
            account_sid = input("Enter your Twilio Account SID: ").strip()
            if len(account_sid) == 34 and account_sid.startswith("AC"):
                updates["TWILIO_ACCOUNT_SID"] = account_sid
                break
            else:
                print("❌ Invalid Account SID format. Should be 34 characters starting with 'AC'")
    else:
        updates["TWILIO_ACCOUNT_SID"] = current_sid
    
    # Get Auth Token
    print("\n" + "-" * 30)
    current_token = current_env.get("TWILIO_AUTH_TOKEN", "")
    if current_token and not current_token.startswith("your_"):
        print("Current Auth Token: [HIDDEN]")
        use_current = input("Use current Auth Token? (y/n): ").lower().strip()
        if use_current != 'y':
            current_token = ""
    
    if not current_token or current_token.startswith("your_"):
        while True:
            auth_token = input("Enter your Twilio Auth Token: ").strip()
            if len(auth_token) == 32:
                updates["TWILIO_AUTH_TOKEN"] = auth_token
                break
            else:
                print("❌ Invalid Auth Token format. Should be 32 characters")
    else:
        updates["TWILIO_AUTH_TOKEN"] = current_token
    
    # Validate credentials
    print("\n🔍 Validating Twilio credentials...")
    account_sid = updates.get("TWILIO_ACCOUNT_SID", current_env.get("TWILIO_ACCOUNT_SID"))
    auth_token = updates.get("TWILIO_AUTH_TOKEN", current_env.get("TWILIO_AUTH_TOKEN"))
    
    is_valid, result = validate_twilio_credentials(account_sid, auth_token)
    
    if is_valid:
        print(f"✅ Credentials valid! Account: {result}")
        
        # Get phone numbers
        print("\n📞 Fetching your Twilio phone numbers...")
        phone_numbers = get_twilio_phone_numbers(account_sid, auth_token)
        
        if phone_numbers:
            print("Available phone numbers:")
            for i, number in enumerate(phone_numbers, 1):
                print(f"  {i}. {number}")
            
            if len(phone_numbers) == 1:
                updates["TWILIO_PHONE_NUMBER"] = phone_numbers[0]
                print(f"✅ Using phone number: {phone_numbers[0]}")
            else:
                while True:
                    try:
                        choice = int(input(f"Select phone number (1-{len(phone_numbers)}): "))
                        if 1 <= choice <= len(phone_numbers):
                            updates["TWILIO_PHONE_NUMBER"] = phone_numbers[choice - 1]
                            print(f"✅ Selected: {phone_numbers[choice - 1]}")
                            break
                        else:
                            print("❌ Invalid choice")
                    except ValueError:
                        print("❌ Please enter a number")
        else:
            print("⚠️  No phone numbers found. You need to purchase one:")
            print("   1. Go to https://console.twilio.com/")
            print("   2. Navigate to Phone Numbers > Manage > Buy a number")
            print("   3. Choose a number with Voice and SMS capabilities")
            
            manual_number = input("Enter your Twilio phone number (e.g., +**********): ").strip()
            if manual_number:
                updates["TWILIO_PHONE_NUMBER"] = manual_number
    else:
        print(f"❌ Credential validation failed: {result}")
        print("Please check your Account SID and Auth Token")
        return False
    
    # Update .env file
    if updates:
        print("\n💾 Updating .env file...")
        update_env_file(updates)
        print("✅ Configuration saved!")
    
    # Final test
    print("\n🧪 Running final test...")
    os.system("python test_twilio_setup.py")
    
    print("\n🎉 Configuration complete!")
    print("\nNext steps:")
    print("1. Run: python start_twilio_server.py")
    print("2. Copy the webhook URLs displayed")
    print("3. Configure them in your Twilio Console")
    print("4. Test by calling or texting your Twilio number!")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
