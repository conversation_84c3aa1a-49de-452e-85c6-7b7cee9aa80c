#!/usr/bin/env python3
"""
Test script for JARVIS Twilio integration setup
Verifies all components are working correctly
"""

import os
import sys
import requests
import time
from dotenv import dotenv_values

def test_environment_variables():
    """Test if all required environment variables are set"""
    print("🔍 Testing environment variables...")
    
    env_vars = dotenv_values(".env")
    required_vars = [
        "TWILIO_ACCOUNT_SID",
        "TWILIO_AUTH_TOKEN", 
        "TWILIO_PHONE_NUMBER",
        "NGROK_AUTHTOKEN"
    ]
    
    missing_vars = []
    for var in required_vars:
        value = env_vars.get(var)
        if not value or value.startswith("your_"):
            missing_vars.append(var)
        else:
            print(f"  ✅ {var}: {'*' * (len(value) - 4) + value[-4:]}")
    
    if missing_vars:
        print(f"  ❌ Missing or placeholder values: {', '.join(missing_vars)}")
        return False
    
    print("  ✅ All environment variables are set!")
    return True

def test_imports():
    """Test if all required modules can be imported"""
    print("\n🔍 Testing module imports...")
    
    try:
        from flask import Flask
        print("  ✅ Flask imported successfully")
    except ImportError as e:
        print(f"  ❌ Flask import failed: {e}")
        return False
    
    try:
        from twilio.rest import Client
        from twilio.twiml.voice_response import VoiceResponse
        from twilio.twiml.messaging_response import MessagingResponse
        print("  ✅ Twilio SDK imported successfully")
    except ImportError as e:
        print(f"  ❌ Twilio SDK import failed: {e}")
        return False
    
    try:
        from pyngrok import ngrok
        print("  ✅ pyngrok imported successfully")
    except ImportError as e:
        print(f"  ❌ pyngrok import failed: {e}")
        return False
    
    try:
        from Backend.Chatbot import ChatBot
        from Backend.Model import FirstLayerDMM
        print("  ✅ JARVIS modules imported successfully")
    except ImportError as e:
        print(f"  ⚠️  JARVIS modules import warning: {e}")
        print("     This is okay if running in standalone mode")
    
    return True

def test_twilio_credentials():
    """Test Twilio credentials"""
    print("\n🔍 Testing Twilio credentials...")
    
    env_vars = dotenv_values(".env")
    account_sid = env_vars.get("TWILIO_ACCOUNT_SID")
    auth_token = env_vars.get("TWILIO_AUTH_TOKEN")
    
    if not account_sid or account_sid.startswith("your_"):
        print("  ⚠️  Twilio Account SID not configured")
        return False
    
    if not auth_token or auth_token.startswith("your_"):
        print("  ⚠️  Twilio Auth Token not configured")
        return False
    
    try:
        from twilio.rest import Client
        client = Client(account_sid, auth_token)
        
        # Test by fetching account info
        account = client.api.accounts(account_sid).fetch()
        print(f"  ✅ Twilio credentials valid - Account: {account.friendly_name}")
        return True
        
    except Exception as e:
        print(f"  ❌ Twilio credentials test failed: {e}")
        return False

def test_ngrok_availability():
    """Test if ngrok is available"""
    print("\n🔍 Testing ngrok availability...")
    
    try:
        import subprocess
        result = subprocess.run(["ngrok", "version"], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"  ✅ ngrok is available: {version}")
            return True
        else:
            print("  ❌ ngrok command failed")
            return False
    except FileNotFoundError:
        print("  ❌ ngrok not found in PATH")
        print("     Please install ngrok from: https://ngrok.com/download")
        return False
    except Exception as e:
        print(f"  ❌ Error testing ngrok: {e}")
        return False

def test_flask_server():
    """Test if Flask server can start"""
    print("\n🔍 Testing Flask server startup...")
    
    try:
        # Import the server module
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        import twilio_server
        
        print("  ✅ Twilio server module loaded successfully")
        print("  ℹ️  Flask server test passed (import successful)")
        return True
        
    except Exception as e:
        print(f"  ❌ Flask server test failed: {e}")
        return False

def test_jarvis_functionality():
    """Test basic JARVIS functionality"""
    print("\n🔍 Testing JARVIS functionality...")
    
    try:
        from Backend.Chatbot import ChatBot
        from Backend.Model import FirstLayerDMM
        
        # Test decision making
        test_query = "Hello, how are you?"
        decision = FirstLayerDMM(test_query)
        print(f"  ✅ Decision making works: {decision}")
        
        # Test chatbot
        response = ChatBot(test_query)
        print(f"  ✅ Chatbot works: {response[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"  ⚠️  JARVIS functionality test failed: {e}")
        print("     This is okay if running in standalone mode")
        return True  # Don't fail the overall test

def main():
    """Run all tests"""
    print("🤖 JARVIS Twilio Integration Setup Test")
    print("=" * 50)
    
    tests = [
        ("Environment Variables", test_environment_variables),
        ("Module Imports", test_imports),
        ("Twilio Credentials", test_twilio_credentials),
        ("Ngrok Availability", test_ngrok_availability),
        ("Flask Server", test_flask_server),
        ("JARVIS Functionality", test_jarvis_functionality)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"  ❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 All tests passed! Your setup is ready.")
        print("\nNext steps:")
        print("1. Configure your Twilio credentials in .env file")
        print("2. Run: python start_twilio_server.py")
        print("3. Configure webhook URLs in Twilio Console")
    else:
        print("\n⚠️  Some tests failed. Please check the issues above.")
        print("Refer to TWILIO_SETUP_GUIDE.md for detailed setup instructions.")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
