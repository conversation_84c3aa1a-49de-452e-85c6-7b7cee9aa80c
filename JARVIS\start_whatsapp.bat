@echo off
echo Starting JARVIS WhatsApp Bot...
echo.

REM Change to the script directory
cd /d "%~dp0"

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python and try again
    pause
    exit /b 1
)

REM Install required packages if needed
echo Checking and installing required packages...
pip install flask twilio google-generativeai python-dotenv requests

REM Start the WhatsApp bot
echo.
echo Starting JARVIS WhatsApp bot...
python start_whatsapp_bot.py

pause
