#!/usr/bin/env python3
"""
Startup script for JARVIS Twilio integration
Automatically starts ngrok tunnel and Flask webhook server
"""

import subprocess
import time
import sys
import os
import json
import requests
from dotenv import dotenv_values
import threading
import signal
import atexit

# Load environment variables
env_vars = dotenv_values(".env")
NGROK_AUTHTOKEN = env_vars.get("NGROK_AUTHTOKEN")

class TwilioServerManager:
    def __init__(self):
        self.ngrok_process = None
        self.flask_process = None
        self.ngrok_url = None
        
    def start_ngrok(self):
        """Start ngrok tunnel"""
        print("🚀 Starting ngrok tunnel...")
        
        try:
            # Start ngrok with configuration file
            self.ngrok_process = subprocess.Popen([
                "ngrok", "start", "jarvis-twilio", "--config", "ngrok.yml"
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            # Wait a moment for ngrok to start
            time.sleep(3)
            
            # Get the public URL from ngrok API
            self.ngrok_url = self.get_ngrok_url()
            
            if self.ngrok_url:
                print(f"✅ Ngrok tunnel started successfully!")
                print(f"🌐 Public URL: {self.ngrok_url}")
                print(f"🔧 Ngrok Web Interface: http://localhost:4040")
                return True
            else:
                print("❌ Failed to get ngrok URL")
                return False
                
        except FileNotFoundError:
            print("❌ ngrok not found. Please install ngrok first:")
            print("   Download from: https://ngrok.com/download")
            print("   Or install via: choco install ngrok (Windows)")
            return False
        except Exception as e:
            print(f"❌ Error starting ngrok: {e}")
            return False
    
    def get_ngrok_url(self, max_retries=10):
        """Get the public URL from ngrok API"""
        for attempt in range(max_retries):
            try:
                response = requests.get("http://localhost:4040/api/tunnels", timeout=5)
                if response.status_code == 200:
                    tunnels = response.json()["tunnels"]
                    for tunnel in tunnels:
                        if tunnel["config"]["addr"] == "http://localhost:5000":
                            return tunnel["public_url"]
                time.sleep(1)
            except requests.exceptions.RequestException:
                time.sleep(1)
                continue
        return None
    
    def start_flask_server(self):
        """Start Flask webhook server"""
        print("🌐 Starting Flask webhook server...")
        
        try:
            # Start Flask server
            self.flask_process = subprocess.Popen([
                sys.executable, "twilio_server.py"
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            # Wait a moment for Flask to start
            time.sleep(2)
            
            # Check if Flask is running
            try:
                response = requests.get("http://localhost:5000/health", timeout=5)
                if response.status_code == 200:
                    print("✅ Flask server started successfully!")
                    print("🏥 Health check: http://localhost:5000/health")
                    return True
            except requests.exceptions.RequestException:
                pass
            
            print("⚠️  Flask server started but health check failed")
            return True
            
        except Exception as e:
            print(f"❌ Error starting Flask server: {e}")
            return False
    
    def display_webhook_urls(self):
        """Display the webhook URLs for Twilio configuration"""
        if not self.ngrok_url:
            print("❌ No ngrok URL available")
            return
        
        voice_webhook = f"{self.ngrok_url}/webhook/voice"
        sms_webhook = f"{self.ngrok_url}/webhook/sms"
        
        print("\n" + "="*60)
        print("🎯 TWILIO WEBHOOK CONFIGURATION")
        print("="*60)
        print(f"📞 Voice Webhook URL: {voice_webhook}")
        print(f"💬 SMS Webhook URL:   {sms_webhook}")
        print("="*60)
        print("\n📋 Configuration Steps:")
        print("1. Go to Twilio Console: https://console.twilio.com/")
        print("2. Navigate to Phone Numbers > Manage > Active numbers")
        print("3. Click on your Twilio phone number")
        print("4. In the 'Voice & Fax' section:")
        print(f"   - Set Webhook URL to: {voice_webhook}")
        print("   - Set HTTP method to: POST")
        print("5. In the 'Messaging' section:")
        print(f"   - Set Webhook URL to: {sms_webhook}")
        print("   - Set HTTP method to: POST")
        print("6. Click 'Save configuration'")
        print("\n🔄 These URLs will remain consistent as long as this script is running!")
        print("="*60)
    
    def cleanup(self):
        """Clean up processes"""
        print("\n🧹 Cleaning up...")
        
        if self.flask_process:
            print("Stopping Flask server...")
            self.flask_process.terminate()
            try:
                self.flask_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.flask_process.kill()
        
        if self.ngrok_process:
            print("Stopping ngrok tunnel...")
            self.ngrok_process.terminate()
            try:
                self.ngrok_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.ngrok_process.kill()
        
        print("✅ Cleanup complete")
    
    def run(self):
        """Main execution method"""
        print("🤖 JARVIS Twilio Integration Server")
        print("=" * 40)
        
        # Register cleanup function
        atexit.register(self.cleanup)
        signal.signal(signal.SIGINT, lambda s, f: sys.exit(0))
        signal.signal(signal.SIGTERM, lambda s, f: sys.exit(0))
        
        # Check if ngrok auth token is configured
        if not NGROK_AUTHTOKEN:
            print("⚠️  NGROK_AUTHTOKEN not found in .env file")
            print("   Your ngrok tunnel may not work properly")
        
        # Start ngrok tunnel
        if not self.start_ngrok():
            print("❌ Failed to start ngrok. Exiting...")
            return False
        
        # Start Flask server
        if not self.start_flask_server():
            print("❌ Failed to start Flask server. Exiting...")
            self.cleanup()
            return False
        
        # Display configuration information
        self.display_webhook_urls()
        
        print("\n🎉 Server is running! Press Ctrl+C to stop.")
        print("📊 Monitor ngrok traffic at: http://localhost:4040")
        print("🏥 Flask health check at: http://localhost:5000/health")
        
        try:
            # Keep the script running
            while True:
                time.sleep(1)
                
                # Check if processes are still running
                if self.ngrok_process and self.ngrok_process.poll() is not None:
                    print("⚠️  Ngrok process died. Restarting...")
                    self.start_ngrok()
                
                if self.flask_process and self.flask_process.poll() is not None:
                    print("⚠️  Flask process died. Restarting...")
                    self.start_flask_server()
                    
        except KeyboardInterrupt:
            print("\n👋 Shutting down...")
            return True

def main():
    """Main function"""
    # Change to script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    # Check if required files exist
    required_files = ["twilio_server.py", "ngrok.yml", ".env"]
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"❌ Missing required files: {', '.join(missing_files)}")
        return False
    
    # Start the server manager
    manager = TwilioServerManager()
    return manager.run()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
