from random import randint
from PIL import Image
import requests
from dotenv import dotenv_values
import os
from time import sleep
from typing import Literal

# Define valid image sizes
ImageSize = Literal['auto', '1024x1024', '1536x1024', '1024x1536', '256x256', '512x512', '1792x1024', '1024x1792']

# Try to import A4F library, fallback if not available
try:
    from openai import OpenAI
    A4F_AVAILABLE = True
except ImportError:
    A4F_AVAILABLE = False
    print("A4F library not installed. Install with: pip install a4f-local openai")

def open_images(prompt):
    folder_path = "Data"
    prompt = prompt.replace(" ", "_")

    files = [f"{prompt}{i}.jpg" for i in range(1, 5)]
    for jpg_file in files:
        image_path = os.path.join(folder_path, f'generated_{jpg_file}')
        try:
            img = Image.open(image_path)
            print(f"Opening image: {image_path}")
            img.show()
            sleep(1)
        except IOError:
            print(f"Unable to open {image_path}")

# Load environment variables
env_vars = dotenv_values(".env")
a4f_api_key = env_vars.get("A4fAPIKey")
a4f_base_url = "https://api.a4f.co/v1"

# Initialize A4F client (OpenAI-compatible)
a4f_client = None
if A4F_AVAILABLE and a4f_api_key:
    try:
        a4f_client = OpenAI( # type: ignore
            api_key=a4f_api_key,
            base_url=a4f_base_url
        )
        print("A4F client initialized successfully")
    except Exception as e:
        print(f"Failed to initialize A4F client: {e}")
        a4f_client = None

def generate_image_with_a4f_single(prompt: str, image_number: int, size: ImageSize = "1024x1024"):
    """Generate single image using A4F API with free tier models"""
    if not a4f_client:
        return False

    try:
        # Use A4F image generation models (actual image generation models)
        image_models_to_try = [
            "provider-5/gpt-image-1",
            "provider-5/dall-e-3",
            "provider-1/FLUX.1-schnell",
            "provider-2/FLUX.1-schnell",
            "provider-3/FLUX.1-schnell",
            "provider-5/FLUX.1 [schnell]",
            "provider-1/FLUX.1.1-pro"
        ]

        for model in image_models_to_try:
            try:
                # Try using the images.generate endpoint
                response = a4f_client.images.generate(
                    model=model,
                    prompt=prompt,
                    n=1,
                    size=size,
                    response_format="url"
                )

                # Check if response is valid and has data
                if response and response.data and len(response.data) > 0:
                    image_url = response.data[0].url
                    if image_url:
                        img_response = requests.get(image_url, timeout=15)

                        if img_response.status_code == 200:
                            original_prompt = prompt.split(", variation")[0]
                            filename = f"generated_{original_prompt.replace(' ', '_')}{image_number}.jpg"
                            filepath = os.path.join("Data", filename)

                            with open(filepath, "wb") as f:
                                f.write(img_response.content)

                            print(f"A4F image {image_number} generated with {model}: {filepath}")
                            return True
                        else:
                            print(f"Failed to download A4F image {image_number}: {img_response.status_code}")
                    else:
                        print(f"No image URL returned from {model}")
                else:
                    print(f"Invalid response from {model}")

            except Exception as model_error:
                error_msg = str(model_error)
                print(f"A4F model {model} failed: {error_msg[:100]}...")
                continue

        # If all A4F image models failed, return False to use fallback
        print(f"All A4F image models failed for image {image_number}")
        return False

    except Exception as e:
        print(f"A4F failed for image {image_number}: {str(e)[:50]}...")
        return False

def generate_image_via_chat_description(prompt: str, image_number: int, size: ImageSize = "1024x1024"):
    """Use A4F chat models to enhance prompt, then generate with fallback API"""
    if not a4f_client:
        return False

    try:
        # Use a free chat model to enhance the image prompt
        enhanced_prompt_response = a4f_client.chat.completions.create(
            model="provider-5/gpt-3.5-turbo",  # Use free tier model
            messages=[
                {"role": "system", "content": "You are an expert at creating detailed image generation prompts. Enhance the user's prompt with artistic details, style, lighting, and composition to create a better image."},
                {"role": "user", "content": f"Enhance this image prompt: {prompt}"}
            ],
            max_tokens=150
        )

        # Check if response is valid and has content
        if (enhanced_prompt_response and enhanced_prompt_response.choices and
            len(enhanced_prompt_response.choices) > 0 and
            enhanced_prompt_response.choices[0].message and
            enhanced_prompt_response.choices[0].message.content):

            enhanced_prompt = enhanced_prompt_response.choices[0].message.content.strip()
            print(f"Enhanced prompt: {enhanced_prompt[:100]}...")
        else:
            print("Failed to get enhanced prompt, using original")
            enhanced_prompt = prompt

        # Use the enhanced prompt with fallback API
        # Convert size format to width and height
        if size == "auto":
            width, height = 1024, 1024
        else:
            width, height = map(int, size.split('x'))

        api_url = f"https://image.pollinations.ai/prompt/{enhanced_prompt.replace(' ', '%20')}?width={width}&height={height}&seed={randint(0, 1000000)}"

        response = requests.get(api_url, timeout=30)
        if response.status_code == 200:
            original_prompt = prompt.split(", variation")[0]
            filename = f"generated_{original_prompt.replace(' ', '_')}{image_number}.jpg"
            filepath = os.path.join("Data", filename)

            with open(filepath, "wb") as f:
                f.write(response.content)

            print(f"A4F-enhanced image {image_number} generated: {filepath}")
            return True
        else:
            print(f"Enhanced image generation failed: {response.status_code}")
            return False

    except Exception as e:
        print(f"Chat-enhanced image generation failed: {str(e)[:50]}...")
        return False

def generate_fallback_images(prompt: str, start_index: int = 1, size: ImageSize = "1024x1024"):
    """Fallback image generation using free API"""
    try:
        # Create Data directory if it doesn't exist
        os.makedirs("Data", exist_ok=True)

        # Use a free image generation API as fallback
        for i in range(start_index, 5):  # Generate from start_index to 4
            try:
                # Using Pollinations AI (free, no API key required)
                # Convert size format to width and height
                if size == "auto":
                    width, height = 1024, 1024
                else:
                    width, height = map(int, size.split('x'))

                api_url = f"https://image.pollinations.ai/prompt/{prompt.replace(' ', '%20')}?width={width}&height={height}&seed={randint(0, 1000000)}"

                response = requests.get(api_url, timeout=30)
                if response.status_code == 200:
                    filename = f"generated_{prompt.replace(' ', '_')}{i}.jpg"
                    filepath = os.path.join("Data", filename)

                    with open(filepath, "wb") as f:
                        f.write(response.content)
                    print(f"Fallback image {i} generated: {filepath}")
                else:
                    print(f"Failed to generate fallback image {i}: {response.status_code}")

                sleep(1)  # Rate limiting

            except Exception as e:
                print(f"Error generating fallback image {i}: {e}")

    except Exception as e:
        print(f"Fallback image generation failed: {e}")

def GenerateImages(prompt: str, size: ImageSize = "1024x1024"):
    """Main image generation function with A4F priority"""
    # Create Data directory if it doesn't exist
    os.makedirs("Data", exist_ok=True)

    success_count = 0

    # Try A4F first (user's preferred method)
    if a4f_client and a4f_api_key:
        print("Using A4F API to enhance prompts and generate images...")
        # Try just one image first to see if any model works
        if generate_image_with_a4f_single(prompt, 1, size):
            success_count = 1
            print("A4F enhancement working! Generating remaining images...")
            # If first image worked, try generating the rest
            for i in range(2, 5):  # Generate remaining 3 images
                variant_prompt = f"{prompt}, variation {i}"
                if generate_image_with_a4f_single(variant_prompt, i, size):
                    success_count += 1
        else:
            print("A4F enhancement failed, using basic fallback...")
    elif not A4F_AVAILABLE:
        print("A4F library not installed. Install with: pip install a4f-local openai")
        print("Using basic fallback method...")
    else:
        print("No A4F API key found, using basic fallback...")

    # Use fallback for any missing images
    if success_count < 4:
        print(f"Generating {4-success_count} images using fallback method...")
        generate_fallback_images(prompt, start_index=success_count+1, size=size)

    print(f"Total images generated: 4")
    # Open the generated images
    open_images(prompt)

# Main loop to process image generation requests
# File format: "prompt,status" or "prompt,status,size"
# Example: "beautiful sunset,True,1920x1080" for 16:9 aspect ratio
# Available sizes: auto, 1024x1024, 1536x1024, 1024x1536, 256x256, 512x512, 1792x1024, 1024x1792
while True:
    try:
        with open("Frontend/Files/ImageGeneration.data", "r") as f:
            data: str = f.read().strip()

        if "," not in data or not data:
            print("Invalid data format in ImageGeneration.data. Skipping...")
            sleep(1)
            continue

        # Support both old format (prompt,status) and new format (prompt,status,size)
        parts = data.split(",")
        if len(parts) >= 2:
            prompt = parts[0].strip()
            status = parts[1].strip()
            size_str = parts[2].strip() if len(parts) > 2 else "1024x1024"

            # Validate size parameter and cast to proper type
            valid_sizes: list[ImageSize] = ['auto', '1024x1024', '1536x1024', '1024x1536', '256x256', '512x512', '1792x1024', '1024x1792']
            if size_str in valid_sizes:
                size: ImageSize = size_str  # type: ignore
            else:
                print(f"Invalid size '{size_str}', using default '1024x1024'")
                size = "1024x1024"
        else:
            print("Invalid data format in ImageGeneration.data. Skipping...")
            sleep(1)
            continue

        if status == "True":
            print(f"Generating Images with size {size}...")
            GenerateImages(prompt=prompt, size=size)

            with open("Frontend/Files/ImageGeneration.data", "w") as f:
                f.write("False, False")
                break
        else:
            sleep(1)
    except Exception as e:
        print(f"An error occurred: {e}")
        sleep(1)
