@echo off
echo Starting JARVIS Twilio Integration Server...
echo.

REM Change to the script directory
cd /d "%~dp0"

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python and try again
    pause
    exit /b 1
)

REM Install required packages if needed
echo Checking and installing required packages...
pip install -r Requirements.txt

REM Start the Twilio server
echo.
echo Starting JARVIS Twilio server...
python start_twilio_server.py

pause
