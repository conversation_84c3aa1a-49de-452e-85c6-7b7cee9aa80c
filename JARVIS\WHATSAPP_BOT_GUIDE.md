# 📱 JARVIS WhatsApp Bot Setup Guide

Transform your JARVIS AI assistant into a WhatsApp chatbot! This setup integrates with both your existing JARVIS system and Google's Gemini API for the best AI experience.

## 🎯 What You Get

- **📱 WhatsApp Integration**: Chat with JARVIS directly on WhatsApp
- **🤖 Dual AI Backend**: Uses both JARVIS and Gemini AI for responses
- **🔄 Smart Fallback**: If JARVIS fails, <PERSON> takes over
- **🚀 Easy Setup**: One command to start everything
- **📊 Monitoring**: Health checks and traffic monitoring

## ⚡ Quick Start (3 Steps)

### 1. Start the Bot
```bash
# Option 1: Python script
python start_whatsapp_bot.py

# Option 2: Windows batch file
start_whatsapp.bat
```

### 2. Get Your Webhook URL
The script will display something like:
```
🔗 Webhook URL: https://abc123.ngrok.io/whatsapp
```

### 3. Configure Twilio WhatsApp Sandbox
1. Go to [Twilio WhatsApp Sandbox](https://console.twilio.com/us1/develop/sms/try-it-out/whatsapp-learn)
2. Send the join code to the sandbox number
3. Set webhook URL to your ngrok URL
4. Start chatting!

## 📋 Prerequisites

1. **Twilio Account** (free tier works)
2. **ngrok** installed and configured
3. **Gemini API Key** (optional but recommended)
4. **JARVIS** system (optional, will work without it)

## 🔧 Configuration

### Environment Variables (.env file)
Make sure these are set in your `.env` file:

```env
# Required for ngrok
NGROK_AUTHTOKEN=your_ngrok_token

# Optional but recommended for better AI responses
GEMINI_API_KEY=your_gemini_api_key

# JARVIS configuration (if available)
Assistantname=Jarvis
Username=YourName
```

### Getting Gemini API Key
1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Add it to your `.env` file as `GEMINI_API_KEY`

## 🤖 AI Backend Features

### JARVIS Integration (Primary)
- Uses your existing JARVIS decision-making system
- Real-time search capabilities
- Automation functions
- Personalized responses

### Gemini AI (Fallback)
- Google's latest AI model (gemini-2.0-flash-exp)
- Context-aware responses
- Handles complex queries
- Fast response times

### Smart Processing
1. **First**: Tries JARVIS AI system
2. **Fallback**: Uses Gemini if JARVIS fails
3. **Final**: Basic error message if both fail

## 📱 WhatsApp Setup Instructions

### Step 1: Access Twilio Console
Go to: https://console.twilio.com/us1/develop/sms/try-it-out/whatsapp-learn

### Step 2: Join Sandbox
1. You'll see a sandbox WhatsApp number (like ****** 523 8886)
2. You'll see a join code (like "join <random-words>")
3. Send the join code to the sandbox number via WhatsApp
4. Wait for confirmation message

### Step 3: Configure Webhook
1. In the Twilio Console, find "Sandbox Configuration"
2. Set "When a message comes in" to: `https://your-ngrok-url.ngrok.io/whatsapp`
3. Set HTTP method to: **POST**
4. Click "Save Configuration"

### Step 4: Test Your Bot
Send messages to the sandbox number:
- "Hello Jarvis"
- "What's the weather today?"
- "Tell me a joke"
- "Search for Python tutorials"

## 🧪 Testing Your Setup

### Health Check
Visit: `http://localhost:5002/health`

Response should show:
```json
{
  "status": "healthy",
  "service": "Jarvis WhatsApp Bot",
  "jarvis_available": true,
  "gemini_available": true
}
```

### Test Endpoint
Visit: `http://localhost:5002/test`

This will test AI functionality without WhatsApp.

### Ngrok Monitoring
Visit: `http://localhost:4040`

Monitor all webhook requests and responses.

## 📊 Monitoring and Debugging

### Console Output
The startup script shows:
- ✅ Service status
- 🌐 Webhook URLs
- 📱 Setup instructions
- ⚠️ Error messages

### Log Messages
- **INFO**: Normal operations
- **WARNING**: Non-critical issues
- **ERROR**: Problems that need attention

### Common Issues

**"JARVIS modules not available"**
- This is normal if running outside JARVIS directory
- Bot will use Gemini API only

**"Gemini API not configured"**
- Add `GEMINI_API_KEY` to `.env` file
- Bot will use JARVIS only

**"ngrok not found"**
- Install ngrok from https://ngrok.com/download
- Add to system PATH

## 💬 Usage Examples

### General Chat
- "Hello, how are you?"
- "Tell me about yourself"
- "What can you do?"

### Information Queries
- "What's the weather in New York?"
- "Latest news about AI"
- "Explain quantum computing"

### JARVIS Functions (if available)
- "Open YouTube"
- "Search for Python tutorials"
- "What's the time?"

### Creative Tasks
- "Tell me a joke"
- "Write a short poem"
- "Explain something interesting"

## 🔒 Security Notes

- WhatsApp sandbox is for testing only
- Don't share sensitive information
- Monitor usage to avoid API limits
- Keep API keys secure

## 🚀 Production Deployment

For production use:
1. Get a Twilio WhatsApp Business Account
2. Use a cloud server instead of ngrok
3. Set up proper SSL certificates
4. Implement rate limiting
5. Add conversation logging

## 📁 Files Created

| File | Purpose |
|------|---------|
| `whatsapp_bot.py` | Main WhatsApp bot Flask server |
| `start_whatsapp_bot.py` | Automated startup script |
| `start_whatsapp.bat` | Windows batch file |
| `WHATSAPP_BOT_GUIDE.md` | This setup guide |

## 🎉 Success!

Once setup is complete, you'll have:
- A WhatsApp number connected to your AI
- Dual AI backend (JARVIS + Gemini)
- Real-time monitoring and debugging
- Production-ready webhook setup

**Your JARVIS AI is now on WhatsApp! 📱🤖**

---

*Happy chatting with your AI assistant on WhatsApp!*
